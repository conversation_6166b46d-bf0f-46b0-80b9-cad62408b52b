{"createdAt": "2025-06-24T18:44:37.066Z", "updatedAt": "2025-06-24T19:34:06.348Z", "id": "KyVzJXzAhtZZDBrR", "name": "gtree_creator", "active": false, "isArchived": false, "nodes": [{"parameters": {"workflowInputs": {"values": [{"name": "path"}, {"name": "owner"}, {"name": "repo"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [0, -125], "id": "f621145a-08a9-4372-a6d9-9c1bcf7ac93d", "name": "When Executed by Another Workflow"}, {"parameters": {"resource": "file", "operation": "list", "owner": {"__rl": true, "value": "={{ $json.owner }}", "mode": "name"}, "repository": {"__rl": true, "value": "={{ $json.repo }}", "mode": "name"}, "filePath": "={{ $json.path }}"}, "type": "n8n-nodes-base.github", "typeVersion": 1.1, "position": [440, -125], "id": "178f72c4-d515-45e6-9a88-551846cfe177", "name": "GitHub", "webhookId": "fb3f4826-5001-4cb2-8ae5-9282374d89c7", "credentials": {"githubApi": {"id": "uJglpW9ievZkAsDT", "name": "GitHub account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "72d30203-bbfd-4557-afed-e41df6125a24", "leftValue": "={{ $json.type }}", "rightValue": "dir", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [660, -125], "id": "3895f2bb-19f8-410d-87a5-b08e4e9123d3", "name": "If dir"}, {"parameters": {"workflowId": {"__rl": true, "value": "KyVzJXzAhtZZDBrR", "mode": "list", "cachedResultName": "gtree_creator"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"path": "={{ $json.path }}", "owner": "={{ $('Edit Fields').item.json.owner }}", "repo": "={{ $('Edit Fields').item.json.repo }}"}, "matchingColumns": [], "schema": [{"id": "path", "displayName": "path", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "owner", "displayName": "owner", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "repo", "displayName": "repo", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [880, -260], "id": "1a6e44ec-bce8-4eee-8978-5f76d32cc6a4", "name": "Execute Workflow"}, {"parameters": {"mode": "raw", "jsonOutput": "={\n  \"path\": \"{{ $json.path ? $json.path : \"\"}}\",\n  \"owner\": \"{{ $json.owner }}\",\n  \"repo\" : \"{{ $json.repo }}\"\n}", "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [220, -125], "id": "1e4c40f6-e872-498d-8e44-bcf273e0215f", "name": "<PERSON>"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [1100, -125], "id": "f947a25d-12ff-4be0-b258-aa831f90abab", "name": "<PERSON><PERSON>"}], "connections": {"When Executed by Another Workflow": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "GitHub": {"main": [[{"node": "If dir", "type": "main", "index": 0}]]}, "If dir": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Edit Fields": {"main": [[{"node": "GitHub", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "9b7f705a-55f9-4706-b5d1-3abc60abd7ba", "triggerCount": 0, "tags": []}