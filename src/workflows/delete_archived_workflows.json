{"createdAt": "2025-06-20T19:31:04.736Z", "updatedAt": "2025-06-20T19:45:32.280Z", "id": "vhExLEjOQch5s0QX", "name": "delete_archived_workflows", "active": false, "isArchived": false, "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-20, -40], "id": "c033c73a-c342-4736-941d-fa63c12064ec", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"filters": {}, "requestOptions": {}}, "type": "n8n-nodes-base.n8n", "typeVersion": 1, "position": [200, -40], "id": "5874fc94-dbe8-456f-afc3-52c0eb302162", "name": "n8n", "credentials": {"n8nApi": {"id": "KOqGA1i2EbNkTR2k", "name": "n8n account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d75e43c9-135d-4df8-a8fa-ee6d717477ab", "leftValue": "={{ $json.isArchived }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [420, -40], "id": "c0472987-4e5e-45cd-8554-dc5e7acf2789", "name": "If"}, {"parameters": {"operation": "delete", "workflowId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "requestOptions": {}}, "type": "n8n-nodes-base.n8n", "typeVersion": 1, "position": [640, -40], "id": "4ae96c95-186e-447c-ac13-28cbc48db2db", "name": "n8n1", "credentials": {"n8nApi": {"id": "KOqGA1i2EbNkTR2k", "name": "n8n account"}}}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}, "n8n": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "n8n1", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "58784bd6-ba71-4a72-979c-cf16eccc7e8c", "triggerCount": 0, "tags": []}